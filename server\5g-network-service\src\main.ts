import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const logger = new Logger('5G-Network-Service');
  
  const app = await NestFactory.create(AppModule);
  
  // 启用CORS
  app.enableCors({
    origin: true,
    credentials: true,
  });

  // 设置全局前缀
  app.setGlobalPrefix('api/v1');

  // 配置Swagger文档
  const config = new DocumentBuilder()
    .setTitle('5G网络深度应用服务')
    .setDescription('5G专网部署、超低延迟通信、大规模设备连接、网络切片管理API')
    .setVersion('1.0')
    .addTag('5g-network', '5G网络管理')
    .addTag('network-slice', '网络切片管理')
    .addTag('device', '设备连接管理')
    .addTag('performance', '性能监控')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  const port = process.env.PORT || 3015;
  await app.listen(port);
  
  logger.log(`5G网络服务已启动，端口: ${port}`);
  logger.log(`API文档地址: http://localhost:${port}/api/docs`);
}

bootstrap().catch(err => {
  console.error('5G网络服务启动失败:', err);
  process.exit(1);
});
